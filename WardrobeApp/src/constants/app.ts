// 应用基本信息
export const APP_INFO = {
  name: 'Wardrobe App',
  version: '1.0.0',
  description: 'AI-powered wardrobe management application',
};

// 搜索相关常量
export const SEARCH_CONFIG = {
  debounceDelay: 300, // 搜索防抖延迟（毫秒）
  minSearchLength: 1, // 最小搜索长度
  maxResults: 100, // 最大搜索结果数
};

// 图片相关常量
export const IMAGE_CONFIG = {
  maxWidth: 1024,
  maxHeight: 1024,
  quality: 0.8,
  allowsEditing: true,
  aspect: [1, 1] as [number, number],
};

// 存储键名
export const STORAGE_KEYS = {
  CLOTHING_ITEMS: 'clothing_items',
  FAVORITES: 'favorites',
  USER_PREFERENCES: 'user_preferences',
  CATEGORIES: 'categories',
};

// 网络请求配置
export const API_CONFIG = {
  timeout: 10000, // 10秒超时
  retryAttempts: 3,
  retryDelay: 1000,
};

// 布局常量
export const LAYOUT = {
  GRID_COLUMNS: 2,
  FAB_BOTTOM_MARGIN: 100,
  HEADER_HEIGHT: 60,
};
