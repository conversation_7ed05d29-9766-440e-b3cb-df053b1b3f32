import { useState, useMemo } from 'react';

interface UseSearchProps<T> {
  data: T[];
  searchFields: (keyof T)[];
  filterFn?: (item: T, query: string) => boolean;
}

interface UseSearchReturn<T> {
  searchQuery: string;
  showSearchBar: boolean;
  filteredData: T[];
  handleSearchPress: () => void;
  handleSearchChange: (query: string) => void;
  setSearchQuery: (query: string) => void;
  setShowSearchBar: (show: boolean) => void;
}

export function useSearch<T>({
  data,
  searchFields,
  filterFn,
}: UseSearchProps<T>): UseSearchReturn<T> {
  const [searchQuery, setSearchQuery] = useState('');
  const [showSearchBar, setShowSearchBar] = useState(false);

  const handleSearchPress = () => {
    setShowSearchBar(!showSearchBar);
    if (showSearchBar) {
      setSearchQuery(''); // 清空搜索内容
    }
  };

  const handleSearchChange = (query: string) => {
    setSearchQuery(query);
  };

  const filteredData = useMemo(() => {
    if (!searchQuery.trim()) {
      return data;
    }

    const lowerQuery = searchQuery.toLowerCase();

    return data.filter(item => {
      // 如果提供了自定义过滤函数，使用它
      if (filterFn) {
        return filterFn(item, lowerQuery);
      }

      // 默认过滤逻辑：在指定字段中搜索
      return searchFields.some(field => {
        const value = item[field];
        if (typeof value === 'string') {
          return value.toLowerCase().includes(lowerQuery);
        }
        if (Array.isArray(value)) {
          return value.some(v => 
            typeof v === 'string' && v.toLowerCase().includes(lowerQuery)
          );
        }
        return false;
      });
    });
  }, [data, searchQuery, searchFields, filterFn]);

  return {
    searchQuery,
    showSearchBar,
    filteredData,
    handleSearchPress,
    handleSearchChange,
    setSearchQuery,
    setShowSearchBar,
  };
}

export default useSearch;
