import React from 'react';
import { View, StyleSheet, TouchableOpacity } from 'react-native';
import { Text, Searchbar } from 'react-native-paper';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { FashionDesignSystem } from '../../styles/FashionDesignSystem';

interface HeaderComponentProps {
  title: string;
  showSearchBar: boolean;
  searchQuery: string;
  onSearchPress: () => void;
  onSearchChange: (query: string) => void;
  searchPlaceholder?: string;
  rightIcon?: string;
  onRightIconPress?: () => void;
}

const HeaderComponent: React.FC<HeaderComponentProps> = ({
  title,
  showSearchBar,
  searchQuery,
  onSearchPress,
  onSearchChange,
  searchPlaceholder = "搜索...",
  rightIcon,
  onRightIconPress,
}) => {
  return (
    <>
      {/* Header with title and search */}
      <View style={styles.header}>
        <Text style={styles.headerTitle}>{title}</Text>
        <View style={styles.headerActions}>
          <TouchableOpacity style={styles.actionButton} onPress={onSearchPress}>
            <Icon 
              name={showSearchBar ? "close" : "search"} 
              size={24} 
              color={FashionDesignSystem.colors.primaryText} 
            />
          </TouchableOpacity>
          {rightIcon && onRightIconPress && (
            <TouchableOpacity style={styles.actionButton} onPress={onRightIconPress}>
              <Icon 
                name={rightIcon} 
                size={24} 
                color={FashionDesignSystem.colors.primaryText} 
              />
            </TouchableOpacity>
          )}
        </View>
      </View>

      {/* Search Bar - conditionally shown */}
      {showSearchBar && (
        <View style={styles.searchContainer}>
          <Searchbar
            placeholder={searchPlaceholder}
            onChangeText={onSearchChange}
            value={searchQuery}
            style={styles.searchBar}
            inputStyle={styles.searchInput}
          />
        </View>
      )}
    </>
  );
};

const styles = StyleSheet.create({
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: FashionDesignSystem.spacing.screenPaddingHorizontal,
    paddingVertical: FashionDesignSystem.spacing.lg,
    backgroundColor: FashionDesignSystem.colors.background,
  },

  headerTitle: {
    ...FashionDesignSystem.typography.title,
    fontWeight: '700',
    letterSpacing: 1,
  },

  headerActions: {
    flexDirection: 'row',
    alignItems: 'center',
  },

  actionButton: {
    padding: FashionDesignSystem.spacing.sm,
    marginLeft: FashionDesignSystem.spacing.xs,
  },

  searchContainer: {
    paddingHorizontal: FashionDesignSystem.spacing.screenPaddingHorizontal,
    paddingBottom: FashionDesignSystem.spacing.md,
  },

  searchBar: {
    backgroundColor: FashionDesignSystem.colors.surfaceVariant,
    borderRadius: FashionDesignSystem.borderRadius.lg,
    elevation: 0,
    shadowOpacity: 0,
  },

  searchInput: {
    ...FashionDesignSystem.typography.body,
    color: FashionDesignSystem.colors.primaryText,
  },
});

export default HeaderComponent;
