import React, { useState } from 'react';
import { View, StyleSheet, ScrollView, Image, Alert, Dimensions, TouchableOpacity, SafeAreaView } from 'react-native';
import {
  Text,
  Card,
  TextInput,
  Chip,
  Menu,
  IconButton,
  HelperText,
  Button
} from 'react-native-paper';
import { useNavigation } from '@react-navigation/native';
import { launchImageLibrary, launchCamera, ImagePickerResponse, MediaType } from 'react-native-image-picker';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { ClothingItem, ClothingCategory, ClothingTag } from '../../types';
import { generateId } from '../../utils/helpers';
import { ClothingStorage } from '../../utils/storage';
import { FashionDesignSystem } from '../../styles/FashionDesignSystem';
import FashionButton from '../../components/common/FashionButton';

const { width } = Dimensions.get('window');

// 预设分类
const defaultCategories: ClothingCategory[] = [
  { id: '1', name: '上衣', icon: 'checkroom', isCustom: false, order: 1 },
  { id: '2', name: '裤子', icon: 'straighten', isCustom: false, order: 2 },
  { id: '3', name: '裙子', icon: 'woman', isCustom: false, order: 3 },
  { id: '4', name: '鞋子', icon: 'sports-tennis', isCustom: false, order: 4 },
  { id: '5', name: '袜子', icon: 'socks', isCustom: false, order: 5 },
  { id: '6', name: '帽子', icon: 'sports-baseball', isCustom: false, order: 6 },
  { id: '7', name: '眼镜', icon: 'visibility', isCustom: false, order: 7 },
  { id: '8', name: '项链', icon: 'favorite', isCustom: false, order: 8 },
  { id: '9', name: '耳环', icon: 'hearing', isCustom: false, order: 9 },
  { id: '10', name: '手链', icon: 'watch', isCustom: false, order: 10 },
  { id: '11', name: '文胸', icon: 'favorite-border', isCustom: false, order: 11 },
  { id: '12', name: '内裤', icon: 'favorite-border', isCustom: false, order: 12 },
];

// 预设标签
const defaultTags = {
  season: [
    { id: 's1', name: '春季', type: 'season' as const, color: '#4CAF50' },
    { id: 's2', name: '夏季', type: 'season' as const, color: '#FF9800' },
    { id: 's3', name: '秋季', type: 'season' as const, color: '#FF5722' },
    { id: 's4', name: '冬季', type: 'season' as const, color: '#2196F3' },
  ],
  style: [
    { id: 'st1', name: '休闲', type: 'style' as const, color: '#9C27B0' },
    { id: 'st2', name: '正式', type: 'style' as const, color: '#3F51B5' },
    { id: 'st3', name: '运动', type: 'style' as const, color: '#FF5722' },
    { id: 'st4', name: '商务', type: 'style' as const, color: '#607D8B' },
    { id: 'st5', name: '约会', type: 'style' as const, color: '#E91E63' },
    { id: 'st6', name: '居家', type: 'style' as const, color: '#795548' },
  ],
  material: [
    { id: 'm1', name: '棉质', type: 'material' as const, color: '#4CAF50' },
    { id: 'm2', name: '丝绸', type: 'material' as const, color: '#E91E63' },
    { id: 'm3', name: '羊毛', type: 'material' as const, color: '#607D8B' },
    { id: 'm4', name: '牛仔', type: 'material' as const, color: '#3F51B5' },
    { id: 'm5', name: '皮革', type: 'material' as const, color: '#795548' },
    { id: 'm6', name: '雪纺', type: 'material' as const, color: '#9C27B0' },
  ],
};

interface FormData {
  name: string;
  description: string;
  categoryId: string;
  selectedTags: ClothingTag[];
  images: string[];
}

const UploadScreen: React.FC = () => {
  const navigation = useNavigation();
  const [formData, setFormData] = useState<FormData>({
    name: '',
    description: '',
    categoryId: '',
    selectedTags: [],
    images: [],
  });
  const [loading, setLoading] = useState(false);
  const [categoryMenuVisible, setCategoryMenuVisible] = useState(false);
  const [tagMenuVisible, setTagMenuVisible] = useState(false);
  const [selectedTagType, setSelectedTagType] = useState<'season' | 'style' | 'material'>('season');
  const [customTag, setCustomTag] = useState('');

  const selectedCategory = defaultCategories.find(cat => cat.id === formData.categoryId);

  const handleImagePicker = () => {
    Alert.alert(
      '选择图片',
      '请选择图片来源',
      [
        { text: '取消', style: 'cancel' },
        { text: '相册', onPress: () => openImageLibrary() },
        { text: '拍照', onPress: () => openCamera() },
      ]
    );
  };

  const openImageLibrary = () => {
    const options = {
      mediaType: 'photo' as MediaType,
      quality: 0.8,
      maxWidth: 1000,
      maxHeight: 1000,
    };

    launchImageLibrary(options, (response: ImagePickerResponse) => {
      if (response.assets && response.assets[0]) {
        const imageUri = response.assets[0].uri;
        if (imageUri) {
          setFormData(prev => ({
            ...prev,
            images: [...prev.images, imageUri]
          }));
        }
      }
    });
  };

  const openCamera = () => {
    const options = {
      mediaType: 'photo' as MediaType,
      quality: 0.8,
      maxWidth: 1000,
      maxHeight: 1000,
    };

    launchCamera(options, (response: ImagePickerResponse) => {
      if (response.assets && response.assets[0]) {
        const imageUri = response.assets[0].uri;
        if (imageUri) {
          setFormData(prev => ({
            ...prev,
            images: [...prev.images, imageUri]
          }));
        }
      }
    });
  };

  const removeImage = (index: number) => {
    setFormData(prev => ({
      ...prev,
      images: prev.images.filter((_, i) => i !== index)
    }));
  };

  const selectCategory = (category: ClothingCategory) => {
    setFormData(prev => ({ ...prev, categoryId: category.id }));
    setCategoryMenuVisible(false);
  };

  const toggleTag = (tag: ClothingTag) => {
    setFormData(prev => {
      const isSelected = prev.selectedTags.some(t => t.id === tag.id);
      if (isSelected) {
        return {
          ...prev,
          selectedTags: prev.selectedTags.filter(t => t.id !== tag.id)
        };
      } else {
        return {
          ...prev,
          selectedTags: [...prev.selectedTags, tag]
        };
      }
    });
  };

  const addCustomTag = () => {
    if (customTag.trim()) {
      const newTag: ClothingTag = {
        id: generateId(),
        name: customTag.trim(),
        type: 'custom',
        color: '#666',
      };
      setFormData(prev => ({
        ...prev,
        selectedTags: [...prev.selectedTags, newTag]
      }));
      setCustomTag('');
    }
  };

  const validateForm = (): boolean => {
    if (formData.images.length === 0) {
      Alert.alert('提示', '请至少添加一张图片');
      return false;
    }
    if (!formData.categoryId) {
      Alert.alert('提示', '请选择分类');
      return false;
    }
    return true;
  };

  const handleSave = async () => {
    if (!validateForm()) {
      return;
    }

    setLoading(true);

    try {
      const newItem: ClothingItem = {
        id: generateId(),
        userId: 'current_user', // 实际应用中从用户状态获取
        categoryId: formData.categoryId,
        name: formData.name || '未命名衣物',
        description: formData.description,
        images: formData.images,
        tags: formData.selectedTags,
        isFavorite: false,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      // 保存到本地存储
      await ClothingStorage.addClothingItem(newItem);

      Alert.alert(
        '上传成功',
        '衣物已成功添加到您的衣柜',
        [
          {
            text: '继续添加',
            onPress: () => {
              setFormData({
                name: '',
                description: '',
                categoryId: '',
                selectedTags: [],
                images: [],
              });
            },
          },
          {
            text: '查看衣柜',
            onPress: () => navigation.navigate('Wardrobe' as never),
          },
        ]
      );
    } catch (error) {
      Alert.alert('上传失败', '请稍后重试');
    } finally {
      setLoading(false);
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      {/* Header with title and actions - like wardrobe page */}
      <View style={styles.header}>
        <TouchableOpacity onPress={() => navigation.goBack()} style={styles.backButton}>
          <Icon name="arrow-back" size={24} color={FashionDesignSystem.colors.primaryText} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>ADD ITEM</Text>
        <TouchableOpacity
          onPress={handleSave}
          disabled={loading}
          style={[styles.saveButton, loading && styles.saveButtonDisabled]}
        >
          <Icon name="check" size={24} color={loading ? FashionDesignSystem.colors.lightText : FashionDesignSystem.colors.primaryText} />
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.content} contentContainerStyle={styles.contentContainer} showsVerticalScrollIndicator={false}>
        {/* 图片上传区域 */}
        <Card style={styles.card}>
          <Card.Content>
            <Text style={styles.sectionTitle}>衣物图片</Text>
            <View style={styles.imageContainer}>
              {formData.images.map((uri, index) => (
                <View key={index} style={styles.imageWrapper}>
                  <Image source={{ uri }} style={styles.image} />
                  <IconButton
                    icon="close"
                    size={20}
                    iconColor="white"
                    style={styles.removeImageButton}
                    onPress={() => removeImage(index)}
                  />
                </View>
              ))}
              {formData.images.length < 5 && (
                <TouchableOpacity
                  style={styles.addImageButton}
                  onPress={handleImagePicker}
                >
                  <View style={styles.addImageContent}>
                    <Icon name="add-a-photo" size={32} color="#666" />
                    <Text style={styles.addImageText}>添加图片</Text>
                  </View>
                </TouchableOpacity>
              )}
            </View>
            <HelperText type="info">
              最多可添加5张图片，建议第一张为主图
            </HelperText>
          </Card.Content>
        </Card>

        {/* 基本信息 */}
        <Card style={styles.card}>
          <Card.Content>
            <Text style={styles.sectionTitle}>基本信息</Text>

            <TextInput
              label="衣物名称（可选）"
              value={formData.name}
              onChangeText={(text) => setFormData(prev => ({ ...prev, name: text }))}
              mode="outlined"
              style={styles.input}
              placeholder="例如：白色衬衫"
            />

            <TextInput
              label="描述（可选）"
              value={formData.description}
              onChangeText={(text) => setFormData(prev => ({ ...prev, description: text }))}
              mode="outlined"
              style={styles.input}
              multiline
              numberOfLines={3}
              placeholder="描述衣物的特点、购买时间等"
            />
          </Card.Content>
        </Card>

        {/* 分类选择 */}
        <Card style={styles.card}>
          <Card.Content>
            <Text style={styles.sectionTitle}>选择分类 *</Text>
            <Menu
              visible={categoryMenuVisible}
              onDismiss={() => setCategoryMenuVisible(false)}
              anchor={
                <TouchableOpacity
                  style={styles.categorySelector}
                  onPress={() => setCategoryMenuVisible(true)}
                >
                  <View style={styles.categorySelectorContent}>
                    {selectedCategory ? (
                      <>
                        <Icon name={selectedCategory.icon} size={24} color="#6200EE" />
                        <Text style={styles.categorySelectorText}>{selectedCategory.name}</Text>
                      </>
                    ) : (
                      <Text style={styles.categorySelectorPlaceholder}>请选择分类</Text>
                    )}
                    <Icon name="arrow-drop-down" size={24} color="#666" />
                  </View>
                </TouchableOpacity>
              }
            >
              {defaultCategories.map((category) => (
                <Menu.Item
                  key={category.id}
                  onPress={() => selectCategory(category)}
                  title={category.name}
                  leadingIcon={category.icon}
                />
              ))}
            </Menu>
          </Card.Content>
        </Card>

        {/* 标签选择 */}
        <Card style={styles.card}>
          <Card.Content>
            <Text style={styles.sectionTitle}>添加标签</Text>

            {/* 标签类型切换 */}
            <View style={styles.tagTypeContainer}>
              {(['season', 'style', 'material'] as const).map((type) => (
                <TouchableOpacity
                  key={type}
                  style={[
                    styles.tagTypeButton,
                    selectedTagType === type && styles.tagTypeButtonActive
                  ]}
                  onPress={() => setSelectedTagType(type)}
                >
                  <Text style={[
                    styles.tagTypeText,
                    selectedTagType === type && styles.tagTypeTextActive
                  ]}>
                    {type === 'season' ? '季节' : type === 'style' ? '风格' : '材质'}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>

            {/* 预设标签 */}
            <View style={styles.tagsContainer}>
              {defaultTags[selectedTagType].map((tag) => {
                const isSelected = formData.selectedTags.some(t => t.id === tag.id);
                return (
                  <Chip
                    key={tag.id}
                    selected={isSelected}
                    onPress={() => toggleTag(tag)}
                    style={[
                      styles.tag,
                      isSelected && { backgroundColor: tag.color }
                    ]}
                    textStyle={isSelected ? { color: 'white' } : undefined}
                  >
                    {tag.name}
                  </Chip>
                );
              })}
            </View>

            {/* 自定义标签 */}
            <View style={styles.customTagContainer}>
              <TextInput
                label="自定义标签"
                value={customTag}
                onChangeText={setCustomTag}
                mode="outlined"
                style={styles.customTagInput}
                placeholder="输入自定义标签"
                onSubmitEditing={addCustomTag}
              />
              <Button
                mode="outlined"
                onPress={addCustomTag}
                disabled={!customTag.trim()}
                style={styles.addTagButton}
                compact
              >
                添加
              </Button>
            </View>

            {/* 已选标签 */}
            {formData.selectedTags.length > 0 && (
              <View style={styles.selectedTagsContainer}>
                <Text style={styles.selectedTagsTitle}>已选标签：</Text>
                <View style={styles.selectedTags}>
                  {formData.selectedTags.map((tag) => (
                    <Chip
                      key={tag.id}
                      onClose={() => toggleTag(tag)}
                      style={[styles.selectedTag, { backgroundColor: tag.color }]}
                      textStyle={{ color: 'white' }}
                    >
                      {tag.name}
                    </Chip>
                  ))}
                </View>
              </View>
            )}
          </Card.Content>
        </Card>

        {/* 保存按钮 */}
        <FashionButton
          title={loading ? "Saving..." : "Save to Wardrobe"}
          onPress={handleSave}
          loading={loading}
          disabled={loading}
          variant="primary"
          size="large"
          fullWidth
          style={styles.saveButtonBottom}
        />
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: FashionDesignSystem.colors.background,
  },

  // Header styles - like wardrobe page
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: FashionDesignSystem.spacing.screenPaddingHorizontal,
    paddingVertical: FashionDesignSystem.spacing.lg,
    backgroundColor: FashionDesignSystem.colors.background,
    borderBottomWidth: 1,
    borderBottomColor: FashionDesignSystem.colors.border,
  },

  headerTitle: {
    ...FashionDesignSystem.typography.title,
    fontWeight: '700',
    letterSpacing: 1,
  },

  backButton: {
    padding: FashionDesignSystem.spacing.sm,
  },

  saveButton: {
    padding: FashionDesignSystem.spacing.sm,
  },

  saveButtonDisabled: {
    opacity: 0.5,
  },

  content: {
    flex: 1,
  },

  contentContainer: {
    paddingHorizontal: FashionDesignSystem.spacing.screenPaddingHorizontal,
    paddingVertical: FashionDesignSystem.spacing.lg,
    paddingBottom: 100,
  },
  card: {
    marginBottom: 16,
    elevation: 2,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 16,
    color: '#333',
  },
  imageContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },
  imageWrapper: {
    position: 'relative',
  },
  image: {
    width: (width - 80) / 3,
    height: (width - 80) / 3,
    borderRadius: 8,
  },
  removeImageButton: {
    position: 'absolute',
    top: -8,
    right: -8,
    backgroundColor: 'rgba(0,0,0,0.6)',
    margin: 0,
  },
  addImageButton: {
    width: (width - 80) / 3,
    height: (width - 80) / 3,
    borderRadius: 8,
    borderWidth: 2,
    borderColor: '#ddd',
    borderStyle: 'dashed',
  },
  addImageContent: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  addImageText: {
    marginTop: 8,
    fontSize: 12,
    color: '#666',
  },
  input: {
    marginBottom: 16,
  },
  categorySelector: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    padding: 16,
  },
  categorySelectorContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  categorySelectorText: {
    flex: 1,
    marginLeft: 12,
    fontSize: 16,
    color: '#333',
  },
  categorySelectorPlaceholder: {
    flex: 1,
    fontSize: 16,
    color: '#999',
  },
  tagTypeContainer: {
    flexDirection: 'row',
    marginBottom: 16,
    backgroundColor: '#f0f0f0',
    borderRadius: 8,
    padding: 4,
  },
  tagTypeButton: {
    flex: 1,
    paddingVertical: 8,
    alignItems: 'center',
    borderRadius: 6,
  },
  tagTypeButtonActive: {
    backgroundColor: '#6200EE',
  },
  tagTypeText: {
    color: '#666',
    fontWeight: '500',
  },
  tagTypeTextActive: {
    color: 'white',
  },
  tagsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
    marginBottom: 16,
  },
  tag: {
    marginBottom: 4,
  },
  customTagContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    gap: 12,
    marginBottom: 16,
  },
  customTagInput: {
    flex: 1,
  },
  addTagButton: {
    marginTop: 8,
  },
  selectedTagsContainer: {
    marginTop: 16,
  },
  selectedTagsTitle: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 8,
    color: '#666',
  },
  selectedTags: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  selectedTag: {
    marginBottom: 4,
  },
  saveButton: {
    marginTop: 16,
    marginBottom: 32,
  },
  saveButtonContent: {
    paddingVertical: 8,
  },
});

export default UploadScreen;
