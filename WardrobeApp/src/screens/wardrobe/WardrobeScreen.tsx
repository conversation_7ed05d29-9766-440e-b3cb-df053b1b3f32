import React, { useState } from 'react';
import { View, StyleSheet, FlatList, SafeAreaView, TouchableOpacity } from 'react-native';
import { useNavigation, useFocusEffect } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { RootStackParamList, ClothingItem } from '../../types';
import { ClothingStorage } from '../../utils/storage';
import ClothingCard from '../../components/clothing/ClothingCard';
import EmptyState from '../../components/common/EmptyState';
import CategoryTabs from '../../components/navigation/CategoryTabs';
import HeaderComponent from '../../components/common/HeaderComponent';
import { useSearch } from '../../hooks/useSearch';
import { NAVIGATION_CATEGORIES } from '../../constants';
import { FashionDesignSystem } from '../../styles/FashionDesignSystem';

type WardrobeScreenNavigationProp = NativeStackNavigationProp<RootStackParamList, 'Main'>;



const WardrobeScreen: React.FC = () => {
  const navigation = useNavigation<WardrobeScreenNavigationProp>();
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);
  const [clothingItems, setClothingItems] = useState<ClothingItem[]>([]);
  const [loading, setLoading] = useState(true);

  // 使用自定义搜索hook
  const {
    searchQuery,
    showSearchBar,
    filteredData: searchFilteredItems,
    handleSearchPress,
    handleSearchChange,
  } = useSearch({
    data: clothingItems,
    searchFields: ['name', 'description'],
    filterFn: (item, query) => {
      return (
        (item.name && item.name.toLowerCase().includes(query)) ||
        (item.description && item.description.toLowerCase().includes(query)) ||
        item.tags.some(tag => tag.name.toLowerCase().includes(query))
      );
    },
  });



  // 加载衣物数据
  const loadClothingItems = async () => {
    try {
      const items = await ClothingStorage.getClothingItems();
      setClothingItems(items);
    } catch (error) {
      console.error('Failed to load clothing items:', error);
    } finally {
      setLoading(false);
    }
  };

  // 页面聚焦时重新加载数据
  useFocusEffect(
    React.useCallback(() => {
      loadClothingItems();
    }, [])
  );

  // 筛选衣物 - 先按搜索过滤，再按分类过滤
  const filteredItems = searchFilteredItems.filter(item => {
    return !selectedCategory || item.categoryId === selectedCategory;
  });



  const handleItemPress = (item: ClothingItem) => {
    navigation.navigate('ItemDetail', { itemId: item.id });
  };

  const handleFavoritePress = async (item: ClothingItem) => {
    try {
      await ClothingStorage.updateClothingItem(item.id, { isFavorite: !item.isFavorite });
      loadClothingItems(); // 重新加载数据
    } catch (error) {
      console.error('Failed to update favorite:', error);
    }
  };

  const renderClothingItem = ({ item }: { item: ClothingItem }) => (
    <ClothingCard
      item={item}
      onPress={() => handleItemPress(item)}
      onFavorite={() => handleFavoritePress(item)}
      showFavoriteButton={true}
    />
  );

  const handleAddClothing = () => {
    navigation.navigate('Upload' as never);
  };





  return (
    <SafeAreaView style={styles.container}>
      {/* Header Component */}
      <HeaderComponent
        title="OUR FASHION"
        showSearchBar={showSearchBar}
        searchQuery={searchQuery}
        onSearchPress={handleSearchPress}
        onSearchChange={handleSearchChange}
        searchPlaceholder="搜索衣物、分类或标签..."
      />

      {/* Category Navigation */}
      <CategoryTabs
        categories={NAVIGATION_CATEGORIES}
        activeCategory={selectedCategory || 'all'}
        onCategoryChange={(categoryId) => setSelectedCategory(categoryId === 'all' ? null : categoryId)}
      />

      {/* Products Grid */}
      <View style={styles.productsContainer}>
        {filteredItems.length === 0 ? (
          <EmptyState
            icon="checkroom"
            title={loading ? "Loading..." : "No Products"}
            description={loading ? "Loading your wardrobe..." :
              selectedCategory ? "No items in this category" : "Add your first clothing item"}
            actionText={!loading && !selectedCategory ? "Add Item" : undefined}
            onAction={!loading && !selectedCategory ? handleAddClothing : undefined}
          />
        ) : (
          <FlatList
            data={filteredItems}
            renderItem={renderClothingItem}
            keyExtractor={(item) => item.id}
            numColumns={2}
            showsVerticalScrollIndicator={false}
            contentContainerStyle={styles.productsList}
            columnWrapperStyle={styles.productRow}
          />
        )}
      </View>

      {/* Floating Add Button */}
      <TouchableOpacity
        style={styles.fab}
        onPress={handleAddClothing}
        activeOpacity={0.8}
      >
        <Icon name="add" size={24} color={FashionDesignSystem.colors.background} />
      </TouchableOpacity>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: FashionDesignSystem.colors.background,
  },



  // Products container
  productsContainer: {
    flex: 1,
    paddingHorizontal: FashionDesignSystem.spacing.screenPaddingHorizontal,
  },

  productsList: {
    paddingBottom: 100, // Space for FAB
  },

  productRow: {
    justifyContent: 'space-between',
    paddingHorizontal: FashionDesignSystem.spacing.xs,
  },
  // Floating Action Button
  fab: {
    ...FashionDesignSystem.components.fab,
  },
});

export default WardrobeScreen;
