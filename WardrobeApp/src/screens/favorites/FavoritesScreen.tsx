import React, { useState } from 'react';
import { View, StyleSheet, FlatList, SafeAreaView } from 'react-native';
import { Text, SegmentedButtons } from 'react-native-paper';
import { useNavigation, useFocusEffect } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { RootStackParamList, ClothingItem, Recommendation } from '../../types';
import { DataService } from '../../services/DataService';
import ClothingCard from '../../components/clothing/ClothingCard';
import EmptyState from '../../components/common/EmptyState';
import LoadingSpinner from '../../components/common/LoadingSpinner';
import CategoryTabs from '../../components/navigation/CategoryTabs';
import HeaderComponent from '../../components/common/HeaderComponent';
import { useSearch } from '../../hooks/useSearch';
import { FAVORITES_CATEGORIES } from '../../constants';
import { FashionDesignSystem } from '../../styles/FashionDesignSystem';

type FavoritesScreenNavigationProp = NativeStackNavigationProp<RootStackParamList, 'Main'>;

type TabType = 'items' | 'outfits';

const FavoritesScreen: React.FC = () => {
  const navigation = useNavigation<FavoritesScreenNavigationProp>();
  const [activeTab, setActiveTab] = useState<TabType>('items');
  const [favoriteItems, setFavoriteItems] = useState<ClothingItem[]>([]);
  const [favoriteOutfits, setFavoriteOutfits] = useState<Recommendation[]>([]);
  const [loading, setLoading] = useState(true);

  // 为衣物使用搜索hook
  const itemsSearch = useSearch({
    data: favoriteItems,
    searchFields: ['name', 'description'],
    filterFn: (item, query) => {
      return (
        (item.name && item.name.toLowerCase().includes(query)) ||
        (item.description && item.description.toLowerCase().includes(query)) ||
        item.tags.some(tag => tag.name.toLowerCase().includes(query))
      );
    },
  });

  // 为推荐使用搜索hook
  const outfitsSearch = useSearch({
    data: favoriteOutfits,
    searchFields: ['scene'],
    filterFn: (item, query) => {
      return item.scene.name.toLowerCase().includes(query);
    },
  });

  // 根据当前tab选择搜索结果
  const currentSearch = activeTab === 'items' ? itemsSearch : outfitsSearch;
  const { searchQuery, showSearchBar, handleSearchPress, handleSearchChange } = currentSearch;

  const loadFavorites = async () => {
    setLoading(true);
    try {
      const [items, outfits] = await Promise.all([
        DataService.getFavoriteClothingItems(),
        DataService.getFavoriteRecommendations(),
      ]);
      setFavoriteItems(items);
      setFavoriteOutfits(outfits);
    } catch (error) {
      console.error('Failed to load favorites:', error);
    } finally {
      setLoading(false);
    }
  };

  useFocusEffect(
    React.useCallback(() => {
      loadFavorites();
    }, [])
  );

  const handleItemPress = (item: ClothingItem) => {
    navigation.navigate('ItemDetail', { itemId: item.id });
  };

  const handleFavoritePress = async (item: ClothingItem) => {
    try {
      await DataService.updateClothingItem(item.id, { isFavorite: false });
      loadFavorites(); // 重新加载数据
    } catch (error) {
      console.error('Failed to update favorite:', error);
    }
  };



  const renderClothingItem = ({ item }: { item: ClothingItem }) => (
    <ClothingCard
      item={item}
      onPress={() => handleItemPress(item)}
      onFavorite={() => handleFavoritePress(item)}
      showFavoriteButton={true}
    />
  );

  const renderOutfitItem = ({ item }: { item: Recommendation }) => (
    // TODO: 创建推荐卡片组件
    <View style={styles.outfitCard}>
      <Text>{item.scene.name} 搭配</Text>
    </View>
  );





  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <HeaderComponent
          title="FAVORITES"
          showSearchBar={false}
          searchQuery=""
          onSearchPress={() => {}}
          onSearchChange={() => {}}
        />
        <LoadingSpinner message="Loading favorites..." />
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      {/* Header Component */}
      <HeaderComponent
        title="FAVORITES"
        showSearchBar={showSearchBar}
        searchQuery={searchQuery}
        onSearchPress={handleSearchPress}
        onSearchChange={handleSearchChange}
        searchPlaceholder="搜索收藏的衣物或搭配..."
      />

      {/* Category Navigation */}
      <CategoryTabs
        categories={FAVORITES_CATEGORIES}
        activeCategory={activeTab}
        onCategoryChange={(categoryId) => setActiveTab(categoryId as TabType)}
      />

      {/* Content */}
      <View style={styles.productsContainer}>
        {activeTab === 'items' ? (
          itemsSearch.filteredData.length === 0 ? (
            <EmptyState
              icon="favorite-border"
              title="No Favorite Items"
              description={searchQuery ? "No matching favorite items found" : "Start favoriting items you love"}
              actionText={!searchQuery ? "Browse Wardrobe" : undefined}
              onAction={!searchQuery ? () => navigation.navigate('Wardrobe' as never) : undefined}
            />
          ) : (
            <FlatList
              data={itemsSearch.filteredData}
              renderItem={renderClothingItem}
              keyExtractor={(item) => item.id}
              numColumns={2}
              showsVerticalScrollIndicator={false}
              contentContainerStyle={styles.productsList}
              columnWrapperStyle={styles.productRow}
            />
          )
        ) : (
          outfitsSearch.filteredData.length === 0 ? (
            <EmptyState
              icon="favorite-border"
              title="No Favorite Outfits"
              description={searchQuery ? "No matching favorite outfits found" : "Start favoriting AI recommended outfits"}
              actionText={!searchQuery ? "Get AI Recommendations" : undefined}
              onAction={!searchQuery ? () => navigation.navigate('AIRecommendation' as never) : undefined}
            />
          ) : (
            <FlatList
              data={outfitsSearch.filteredData}
              renderItem={renderOutfitItem}
              keyExtractor={(item) => item.id}
              showsVerticalScrollIndicator={false}
              contentContainerStyle={styles.productsList}
            />
          )
        )}
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: FashionDesignSystem.colors.background,
  },



  // Products container
  productsContainer: {
    flex: 1,
    paddingHorizontal: FashionDesignSystem.spacing.screenPaddingHorizontal,
  },

  productsList: {
    paddingBottom: 100, // Space for FAB
  },

  productRow: {
    justifyContent: 'space-between',
    paddingHorizontal: FashionDesignSystem.spacing.xs,
  },

  outfitCard: {
    backgroundColor: FashionDesignSystem.colors.surface,
    borderRadius: FashionDesignSystem.borderRadius.card,
    padding: FashionDesignSystem.spacing.lg,
    marginBottom: FashionDesignSystem.spacing.md,
    ...FashionDesignSystem.shadows.soft,
  },
});

export default FavoritesScreen;
